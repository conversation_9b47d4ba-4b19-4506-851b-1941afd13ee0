import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from dataclasses import dataclass, field
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SchedulerMetrics:
    """Metrics collection for scheduler engine performance monitoring."""

    # Batch execution metrics
    total_batches_processed: int = 0
    total_schedulers_processed: int = 0
    total_schedulers_queued: int = 0
    total_schedulers_failed: int = 0
    total_execution_time: float = 0.0

    # Error tracking
    engine_errors: int = 0
    last_error: Optional[str] = None
    last_error_time: Optional[datetime] = None

    # Performance metrics
    average_batch_size: float = 0.0
    average_execution_time: float = 0.0
    success_rate: float = 0.0

    # Timing metrics
    last_run_time: Optional[datetime] = None
    metrics_start_time: datetime = field(
        default_factory=lambda: datetime.now(timezone.utc)
    )

    def record_batch_execution(
        self, total: int, successful: int, failed: int, execution_time: float
    ):
        """Record metrics for a batch execution."""
        self.total_batches_processed += 1
        self.total_schedulers_processed += total
        self.total_schedulers_queued += successful
        self.total_schedulers_failed += failed
        self.total_execution_time += execution_time
        self.last_run_time = datetime.now(timezone.utc)

        # Calculate derived metrics
        if self.total_batches_processed > 0:
            self.average_batch_size = (
                self.total_schedulers_processed / self.total_batches_processed
            )
            self.average_execution_time = (
                self.total_execution_time / self.total_batches_processed
            )

        if self.total_schedulers_processed > 0:
            self.success_rate = (
                self.total_schedulers_queued / self.total_schedulers_processed
            ) * 100

        # Only log when there's actual scheduler activity to avoid log spam
        if total > 0:
            logger.info(
                f"Scheduler metrics updated - "
                f"Batch: {total} processed, {successful} queued, {failed} failed, "
                f"Time: {execution_time:.2f}s, "
                f"Success rate: {self.success_rate:.1f}%"
            )

    def record_engine_error(self, error_message: str):
        """Record an engine error."""
        self.engine_errors += 1
        self.last_error = error_message
        self.last_error_time = datetime.now(timezone.utc)

        logger.error(f"Scheduler engine error recorded: {error_message}")

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        uptime = (datetime.now(timezone.utc) - self.metrics_start_time).total_seconds()

        return {
            "uptime_seconds": uptime,
            "total_batches_processed": self.total_batches_processed,
            "total_schedulers_processed": self.total_schedulers_processed,
            "total_schedulers_queued": self.total_schedulers_queued,
            "total_schedulers_failed": self.total_schedulers_failed,
            "success_rate_percent": round(self.success_rate, 2),
            "average_batch_size": round(self.average_batch_size, 2),
            "average_execution_time_seconds": round(self.average_execution_time, 2),
            "total_execution_time_seconds": round(self.total_execution_time, 2),
            "engine_errors": self.engine_errors,
            "last_error": self.last_error,
            "last_error_time": (
                self.last_error_time.isoformat() if self.last_error_time else None
            ),
            "last_run_time": (
                self.last_run_time.isoformat() if self.last_run_time else None
            ),
            "metrics_start_time": self.metrics_start_time.isoformat(),
        }

    def reset(self):
        """Reset all metrics."""
        self.__init__()
        logger.info("Scheduler metrics reset")


@dataclass
class TaskQueueMetrics:
    """Metrics collection for task queue performance monitoring."""

    # Task processing metrics
    tasks_enqueued: int = 0
    tasks_dequeued: int = 0
    tasks_completed: int = 0
    tasks_failed: int = 0
    tasks_retried: int = 0

    # Queue metrics
    queue_sizes: Dict[str, int] = field(default_factory=dict)
    processing_times: Dict[str, float] = field(default_factory=dict)

    # Worker metrics
    active_workers: int = 0
    worker_errors: int = 0

    # Performance metrics
    average_processing_time: float = 0.0
    success_rate: float = 0.0

    # Timing
    metrics_start_time: datetime = field(
        default_factory=lambda: datetime.now(timezone.utc)
    )

    def record_task_enqueued(self, queue_name: str):
        """Record a task being enqueued."""
        self.tasks_enqueued += 1
        logger.debug(
            f"Task enqueued to {queue_name}. Total enqueued: {self.tasks_enqueued}"
        )

    def record_task_dequeued(self, queue_name: str):
        """Record a task being dequeued."""
        self.tasks_dequeued += 1
        logger.debug(
            f"Task dequeued from {queue_name}. Total dequeued: {self.tasks_dequeued}"
        )

    def record_task_completed(self, task_type: str, processing_time: float):
        """Record a task completion."""
        self.tasks_completed += 1
        self.processing_times[task_type] = processing_time

        # Update average processing time
        if self.tasks_completed > 0:
            total_time = sum(self.processing_times.values())
            self.average_processing_time = total_time / len(self.processing_times)

        # Update success rate
        total_processed = self.tasks_completed + self.tasks_failed
        if total_processed > 0:
            self.success_rate = (self.tasks_completed / total_processed) * 100

        logger.debug(
            f"Task {task_type} completed in {processing_time:.2f}s. "
            f"Total completed: {self.tasks_completed}, Success rate: {self.success_rate:.1f}%"
        )

    def record_task_failed(self, task_type: str, error: str):
        """Record a task failure."""
        self.tasks_failed += 1

        # Update success rate
        total_processed = self.tasks_completed + self.tasks_failed
        if total_processed > 0:
            self.success_rate = (self.tasks_completed / total_processed) * 100

        logger.warning(
            f"Task {task_type} failed: {error}. "
            f"Total failed: {self.tasks_failed}, Success rate: {self.success_rate:.1f}%"
        )

    def record_task_retried(self, task_type: str):
        """Record a task retry."""
        self.tasks_retried += 1
        # Removed debug log - retry metrics are available via metrics endpoint

    def record_worker_started(self):
        """Record a worker starting."""
        self.active_workers += 1
        logger.info(f"Worker started. Active workers: {self.active_workers}")

    def record_worker_stopped(self):
        """Record a worker stopping."""
        self.active_workers = max(0, self.active_workers - 1)
        logger.info(f"Worker stopped. Active workers: {self.active_workers}")

    def record_worker_error(self, error: str):
        """Record a worker error."""
        self.worker_errors += 1
        logger.error(
            f"Worker error: {error}. Total worker errors: {self.worker_errors}"
        )

    def update_queue_size(self, queue_name: str, size: int):
        """Update the size of a specific queue."""
        self.queue_sizes[queue_name] = size
        logger.debug(f"Queue {queue_name} size updated to {size}")

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all task queue metrics."""
        uptime = (datetime.now(timezone.utc) - self.metrics_start_time).total_seconds()

        return {
            "uptime_seconds": uptime,
            "tasks_enqueued": self.tasks_enqueued,
            "tasks_dequeued": self.tasks_dequeued,
            "tasks_completed": self.tasks_completed,
            "tasks_failed": self.tasks_failed,
            "tasks_retried": self.tasks_retried,
            "success_rate_percent": round(self.success_rate, 2),
            "average_processing_time_seconds": round(self.average_processing_time, 2),
            "active_workers": self.active_workers,
            "worker_errors": self.worker_errors,
            "queue_sizes": self.queue_sizes,
            "metrics_start_time": self.metrics_start_time.isoformat(),
        }

    def reset(self):
        """Reset all metrics."""
        self.__init__()
        logger.info("Task queue metrics reset")


# Global metrics instances
scheduler_metrics = SchedulerMetrics()
task_queue_metrics = TaskQueueMetrics()
