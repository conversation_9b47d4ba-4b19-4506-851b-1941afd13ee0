"""
Structured logging configuration for the Trigger Service.

This module provides structured JSON logging with correlation IDs for
request tracing and proper log formatting for both development and production.

Features:
- RFC 5424 compatible syslog formatting
- Configurable field filtering
- Full UUID preservation for debugging
- Standard logfmt and JSON formats
- Established python-json-logger integration
- Minimal, necessary logging only
"""

import logging
import sys
import uuid
from contextvars import ContextVar
from typing import Any, Dict, Optional, Set

import structlog
from structlog.types import EventDict, Processor
from pythonjsonlogger import jsonlogger


# Context variable for correlation ID
correlation_id_var: ContextVar[Optional[str]] = ContextVar(
    "correlation_id", default=None
)

# Flag to track if logging has been configured
_logging_configured = False

# Configurable field filtering - can be modified at runtime
FILTERED_FIELDS: Set[str] = {
    # Remove these fields by default to reduce noise
    "service",
    "version",
    "timestamp",
    "adapter_name",
    "status",
    "updated",
    "resource_state",
    "summary",
    "event_summary",
    "event_count",
    "total_events",
    "filtered_events",
    "configured_types",
    "resource_id",
}

# Sensitive data patterns - always filtered for security
SENSITIVE_PATTERNS: Set[str] = {
    "password",
    "token",
    "secret",
    "key",
    "credential",
    "authorization",
    "auth",
    "api",
    "access",
    "refresh",
    "client",
}


def add_correlation_id(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Add correlation ID to log events.

    Args:
        logger: Logger instance
        method_name: Method name being called
        event_dict: Event dictionary to modify

    Returns:
        EventDict: Modified event dictionary with correlation ID
    """
    correlation_id = correlation_id_var.get()
    if correlation_id:
        event_dict["correlation_id"] = correlation_id
    return event_dict


def add_service_info(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """
    Add service information to log events with RFC 5424 compatibility.

    RFC 5424 structured data format: [service@32473 name="trigger-service" version="0.1.0"]

    Args:
        logger: Logger instance
        method_name: Method name being called
        event_dict: Event dictionary to modify

    Returns:
        EventDict: Modified event dictionary with service info
    """
    # Add service info for all levels but make it configurable
    if "service" not in FILTERED_FIELDS:
        event_dict["service"] = "trigger-service"
    if "version" not in FILTERED_FIELDS:
        event_dict["version"] = "0.1.0"

    # RFC 5424 facility and severity for syslog compatibility
    level = event_dict.get("level", "INFO").upper()
    severity_map = {
        "DEBUG": 7,  # Debug
        "INFO": 6,  # Informational
        "WARNING": 4,  # Warning
        "ERROR": 3,  # Error
        "CRITICAL": 2,  # Critical
    }
    event_dict["severity"] = severity_map.get(level, 6)
    event_dict["facility"] = 16  # Local use 0 (16*8 + severity)

    return event_dict


def filter_sensitive_data(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Filter sensitive data from log events using configurable patterns.

    Args:
        logger: Logger instance
        method_name: Method name being called
        event_dict: Event dictionary to modify

    Returns:
        EventDict: Modified event dictionary with sensitive data filtered
    """

    def filter_dict(data: Any) -> Any:
        """Recursively filter sensitive data from dictionary."""
        if isinstance(data, dict):
            filtered = {}
            for key, value in data.items():
                if isinstance(key, str) and any(
                    sensitive in key.lower() for sensitive in SENSITIVE_PATTERNS
                ):
                    filtered[key] = "[REDACTED]"
                elif isinstance(value, dict):
                    filtered[key] = filter_dict(value)
                elif isinstance(value, list):
                    filtered[key] = [
                        filter_dict(item) if isinstance(item, dict) else item
                        for item in value
                    ]
                else:
                    filtered[key] = value
            return filtered
        return data

    # Filter the entire event dict
    return filter_dict(event_dict)


def logfmt_renderer(_, __, event_dict):
    """
    Logfmt renderer for structured text logging.
    Format: timestamp=2023-01-01T12:00:00Z level=INFO logger=service event="message" key=value
    """
    # Extract core information
    timestamp = event_dict.pop("timestamp", "")
    level = event_dict.pop("level", "INFO").upper()
    event = event_dict.pop("event", "")
    logger_name = event_dict.pop("logger", "").split(".")[-1]

    # Remove configurable filtered fields
    for field in FILTERED_FIELDS:
        event_dict.pop(field, None)

    # Build logfmt string
    parts = [
        f"timestamp={timestamp}",
        f"level={level}",
        f"logger={logger_name}",
        f'event="{event}"',
    ]

    # Add remaining fields in logfmt format
    for key, value in event_dict.items():
        if isinstance(value, str):
            parts.append(f'{key}="{value}"')
        else:
            parts.append(f"{key}={value}")

    return " ".join(parts)


def industry_standard_renderer(_, __, event_dict):
    """
    Industry standard console renderer for production-ready logs.
    Format: [TIMESTAMP] LEVEL logger: message [context]
    Preserves full UUIDs and uses configurable field filtering.
    """
    # Extract core information
    timestamp = event_dict.pop("timestamp", "")
    level = event_dict.pop("level", "INFO").upper()
    event = event_dict.pop("event", "")
    logger_name = event_dict.pop("logger", "").split(".")[-1]

    # Remove configurable filtered fields instead of hardcoded list
    for field in FILTERED_FIELDS:
        event_dict.pop(field, None)

    # Skip debug logs for clean production output (configurable)
    if level == "DEBUG":
        return None

    # Industry standard colors
    colors = {
        "INFO": "\033[36m",  # Cyan
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[35m",  # Magenta
    }
    reset = "\033[0m"

    # Standard format: [TIMESTAMP] LEVEL logger: message
    color = colors.get(level, "")
    base_line = f"[{timestamp}] {color}{level:7}{reset} {logger_name:15}: {event}"

    # Add context - preserve full UUIDs for debugging
    context_parts = []

    # Always show full UUIDs - don't truncate for debugging
    if "trigger_id" in event_dict:
        trigger_id = str(event_dict.pop("trigger_id"))
        context_parts.append(f"trigger_id={trigger_id}")

    if "user_id" in event_dict:
        user_id = str(event_dict.pop("user_id"))
        context_parts.append(f"user_id={user_id}")

    if "workflow_id" in event_dict:
        workflow_id = str(event_dict.pop("workflow_id"))
        context_parts.append(f"workflow_id={workflow_id}")

    if "correlation_id" in event_dict:
        correlation_id = str(event_dict.pop("correlation_id"))
        context_parts.append(f"correlation_id={correlation_id}")

    if "trigger_name" in event_dict:
        trigger_name = str(event_dict.pop("trigger_name"))
        context_parts.append(f"trigger_name={trigger_name}")

    if "event_type" in event_dict:
        event_type = str(event_dict.pop("event_type"))
        context_parts.append(f"event_type={event_type}")

    # Error context for issues
    if level in ["ERROR", "WARNING", "CRITICAL"]:
        if "error" in event_dict:
            context_parts.append(f"error={event_dict.pop('error')}")

    # Add any remaining fields
    for key, value in event_dict.items():
        context_parts.append(f"{key}={value}")

    # Add context if present
    if context_parts:
        context_str = " ".join(context_parts)
        return f"{base_line} [{context_str}]"
    else:
        return base_line


def setup_logging(
    log_level: str = "INFO", log_format: str = "json", force_reconfigure: bool = False
) -> None:
    """
    Configure structured logging for the application.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Logging format ("json", "logfmt", or "text")
        force_reconfigure: Force reconfiguration even if already configured
    """
    global _logging_configured

    # Only configure once unless forced
    if _logging_configured and not force_reconfigure:
        return
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
        force=True,  # Force reconfiguration
    )

    # Set root logger level to ensure all loggers respect the global level
    logging.getLogger().setLevel(getattr(logging, log_level.upper()))

    # Silence noisy third-party loggers
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("google").setLevel(logging.WARNING)
    logging.getLogger("googleapiclient").setLevel(logging.WARNING)
    logging.getLogger("google_auth_httplib2").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

    # Configure processors based on format
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        add_correlation_id,
        filter_sensitive_data,  # Filter sensitive data early
        add_service_info,
        structlog.processors.add_log_level,
    ]

    if log_format == "json":
        # Use structlog's JSON renderer for consistent formatting
        processors.extend(
            [
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.JSONRenderer(),
            ]
        )
    elif log_format == "logfmt":
        # Logfmt format for structured text logging
        processors.extend(
            [
                structlog.processors.TimeStamper(fmt="iso"),
                logfmt_renderer,
            ]
        )
    else:
        # Industry standard format for development
        processors.extend(
            [
                structlog.processors.TimeStamper(fmt="iso"),
                industry_standard_renderer,
            ]
        )

    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Mark as configured
    _logging_configured = True


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name (typically __name__)

    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


def set_correlation_id(correlation_id: Optional[str] = None) -> str:
    """
    Set correlation ID for the current context.

    Args:
        correlation_id: Correlation ID to set, or None to generate a new one

    Returns:
        str: The correlation ID that was set
    """
    if correlation_id is None:
        correlation_id = str(uuid.uuid4())

    correlation_id_var.set(correlation_id)
    return correlation_id


def get_correlation_id() -> Optional[str]:
    """
    Get the current correlation ID.

    Returns:
        Optional[str]: Current correlation ID, or None if not set
    """
    return correlation_id_var.get()


def clear_correlation_id() -> None:
    """Clear the current correlation ID."""
    correlation_id_var.set(None)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to other classes.

    This mixin provides a logger property that automatically includes
    the class name in log events.
    """

    @property
    def logger(self) -> structlog.BoundLogger:
        """Get a logger bound to this class."""
        return get_logger(self.__class__.__name__)


def configure_field_filtering(
    filtered_fields: Optional[Set[str]] = None,
    sensitive_patterns: Optional[Set[str]] = None,
) -> None:
    """
    Configure field filtering at runtime.

    Args:
        filtered_fields: Set of field names to filter from logs
        sensitive_patterns: Set of patterns to identify sensitive data
    """
    global FILTERED_FIELDS, SENSITIVE_PATTERNS

    if filtered_fields is not None:
        FILTERED_FIELDS = filtered_fields

    if sensitive_patterns is not None:
        SENSITIVE_PATTERNS = sensitive_patterns


def get_field_filtering_config() -> Dict[str, Set[str]]:
    """
    Get current field filtering configuration.

    Returns:
        Dict containing current filtered_fields and sensitive_patterns
    """
    return {
        "filtered_fields": FILTERED_FIELDS.copy(),
        "sensitive_patterns": SENSITIVE_PATTERNS.copy(),
    }


def create_json_logger(name: str) -> logging.Logger:
    """
    Create a logger using python-json-logger for established JSON formatting.

    Args:
        name: Logger name

    Returns:
        Logger configured with JSON formatter
    """
    logger = logging.getLogger(name)

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = jsonlogger.JsonFormatter(
            fmt="%(asctime)s %(name)s %(levelname)s %(message)s",
            datefmt="%Y-%m-%dT%H:%M:%SZ",
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger
